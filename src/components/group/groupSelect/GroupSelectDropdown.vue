<script setup lang="ts">
import type { IGroupInfo } from "@xiaou66/todo-plugin";
import { computed, onMounted, ref, watch } from 'vue';
import { ExtensionManager } from '@/extension'


const options = ref<{label:string, value: string}[]>([]);
const optionMap = computed<Record<string, any>>(() => {
  return options.value.reduce((obj, cur) => {
    // @ts-ignore
    obj[cur.value] = cur;
    return obj;
  }, {});
})
async function groupList() {
  const taskInstance = ExtensionManager.getGroupInstance();
  const group = await taskInstance.listGroup({} as any);
  options.value = group.list.map((item: IGroupInfo) => ({
    label: item.groupName,
    value: item.groupId,
  }));

  if (modelValue.value !== '') {
    const idx = options.value.findIndex((item) => item.value === modelValue.value);
    if (idx === -1) {
      modelValue.value = '';
    }
  }
  if (modelValue.value === '') {
    modelValue.value = group.list[0].groupId;
  }
}

const modelValue = defineModel('modelValue', {
  default: 'collectBox'
});
watch(modelValue, () => {
  if (!modelValue.value) {
    modelValue.value = 'collectBox'
  } else {
    if (!popupVisible.value && modelValue.value !== 'collectBox') {
      const index = options.value.findIndex(item => item.value === modelValue.value);
      if (index === -1) {
        groupList();
      }
    }
  }
})
const emits = defineEmits<{
  blur: [];
}>();
function handleSelectEvent(value: string) {
  modelValue.value = value;
  emits('blur');
}
const popupVisible = ref(false);
onMounted(() => {
  groupList();
})

watch(() => popupVisible.value, (value) =>{
  if (value) {
    groupList();
  }
});

defineExpose<{refresh: () => Promise<void>}>({
  refresh: groupList,
})
</script>

<template>
  <a-dropdown v-if="modelValue"
              v-model:popup-visible="popupVisible"
              trigger="hover"
              class="min-dropdown-select">
    <a-link class="task-select-group">
      {{modelValue === 'collectBox' || modelValue === '' || !optionMap[modelValue as any] ? '未分组' : optionMap[modelValue as any].label}}
    </a-link>
    <template #content>
      <div class="min-dropdown-select-options">
        <div v-if="modelValue === 'collectBox' && options.length === 0"
             class="u-tips">
          当前没有分组
        </div>
        <a-doption v-show="modelValue !== 'collectBox'"
                   value="collectBox"
                   @click="handleSelectEvent('collectBox')">未分组</a-doption>
        <a-doption v-show="option.value !== modelValue"
                   v-for="option in options"
                   :key="option.value"
                   @click="handleSelectEvent(option.value as string)">
          {{ option.label }}
        </a-doption>
      </div>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">

</style>
