<script setup lang="ts">
import { GroupSelectDropdown } from '../../group/groupSelect'
import { TaskLevelDropdown } from '../'
import { onMounted, readonly, ref, useTemplateRef, watch } from 'vue';
import type { TaskSaveParams } from '@xiaou66/todo-plugin'
import type { TaskCreateInputInstance } from '@/components/Tasks/TaskCreateInput/TaskCreateInput.ts'
import type { InputInstance } from '@arco-design/web-vue'
import { useEventListener } from '@vueuse/core'
import { cloneDeep, merge, mergeWith } from 'es-toolkit';
import jsUtil from '@/utils/jsUtil.ts'
import { TaskDateTimeTrigger } from "./index.ts";
import dayjs from 'dayjs';
import TaskCreateInputTips from "./TaskCreateInputTips.vue";
import TaskTagDropdown from "./TaskTagDropdown.vue";
import hotkeys from "hotkeys-js";

const props = withDefaults(
  defineProps<{
    disableModule: ('taskLevel' | 'groupSelect' | 'calendar')[]
    size?: 'small' | 'large'
    blurSave?: boolean
    disableAutoToday?: boolean
    placeholder?: string;
    direction?: 'bottom' | 'top',
    createDefaultConfig?: Partial<TaskSaveParams>;
  }>(),
  {
    disableModule: () => [],
    size: 'large',
    blurSave: false,
    disableAutoToday: false,
    placeholder: '回车创建',
    direction: 'bottom',
    createDefaultConfig: () => ({}),
  },
)

const createTaskInfo = ref<TaskSaveParams>({
  taskLevel: '2',
  taskGroupId: 'collectBox',
  taskTitle: '',
  taskDateType: 'date'
})

const emits = defineEmits<{
  create: [TaskSaveParams]
  blur: []
}>()
const tipsVisible = ref<boolean>(false);
const taskCreateInputTipsRef = useTemplateRef<{
  inputEvent(value: string, e: InputEvent): void
}>('taskCreateInputTipsRef');
function handleCreateTask() {
  if (!createTaskInfo.value.taskTitle) {
    return
  }
  console.log('tipsVisible.value', tipsVisible.value)
  if (tipsVisible.value) {
    return;
  }
  console.log(createTaskInfo.value)
  console.log('handleCreateTask', cloneDeep(createTaskInfo.value))
  emits('create', cloneDeep(createTaskInfo.value));
  createTaskInfo.value.taskTitle=  '';
}
function handleInputBlurEvent() {
  if (props.blurSave) {
    handleCreateTask()
  }
  emits('blur')
}

function handleInputEvent(value: string, e: InputEvent) {
  taskCreateInputTipsRef.value?.inputEvent(value, e);
}



function setData(task: Partial<TaskSaveParams> = {}) {
  createTaskInfo.value = {
    ...createTaskInfo.value,
    ...task,
  }
}
const inputRef = useTemplateRef<InputInstance>('inputRef')
const taskCreateInputRef = useTemplateRef<HTMLDivElement>('taskCreateInputRef')
defineExpose<TaskCreateInputInstance>({
  focus: () => inputRef.value && inputRef.value.focus(),
  setData,
});

watch(() => props.createDefaultConfig, (value) => {
  Object.keys(value).forEach(key => {
    if (key in createTaskInfo.value && value[key]) {
      createTaskInfo.value[key] = value[key];
    }
  });
});

onMounted(() => {
  createTaskInfo.value = merge(createTaskInfo.value, props.createDefaultConfig);
  if (!props.disableAutoToday) {
    setData({
      taskStartDate: dayjs().format('YYYY-MM-DD'),
    });
  }
  // TODO 暂时处理
  utools.onPluginOut(() => {
    setData({
      taskStartDate: dayjs().format('YYYY-MM-DD'),
    });
  })
})


// 判断点击已经外部触发失去焦点事件
useEventListener('mousedown', (e: MouseEvent) => {
  if (
    taskCreateInputRef.value &&
    e.target &&
    !taskCreateInputRef.value.contains(e.target as Node)
  ) {
    handleInputBlurEvent()
  }
});

hotkeys('command+n,ctrl+n', () => {
  inputRef.value?.focus();
});

function handleTaskDateChance(data: any) {
  console.log('handleTaskDateChance', data);
  createTaskInfo.value = {
    ...createTaskInfo.value,
    ...data,
  };
  inputRef.value?.focus();
}

const groupSelectDropDownRef = useTemplateRef<any>('groupSelectDropDownRef');
function handleInputFocus() {
  groupSelectDropDownRef.value?.refresh();
}

</script>
<template>
  <TaskCreateInputTips ref="taskCreateInputTipsRef"
                       v-model:model-value="createTaskInfo"
                       v-model:tips-visible="tipsVisible"
                       :direction="direction" />
  <div ref="taskCreateInputRef"
       class="task-create-input add-task">
    <a-input
      v-model:model-value="createTaskInfo.taskTitle"
      ref="inputRef"
      :size="size"
      :placeholder="placeholder"
      style="border-radius: 10px; padding: 4px 10px"
      @focus="handleInputFocus"
      @input="handleInputEvent"
      @pressEnter="handleCreateTask">
      <template #prefix>
        <div class="u-fx u-gap5">
          <TaskLevelDropdown
            v-if="!disableModule.includes('taskLevel')"
            v-model:modal-value="createTaskInfo.taskLevel"
            :popup-container="taskCreateInputRef!"
            @blur="() => inputRef!.focus()"
          />
          <GroupSelectDropdown
            v-if="!disableModule.includes('groupSelect')"
            v-model:model-value="createTaskInfo.taskGroupId"
            ref="groupSelectDropDownRef"
            @blur="() => inputRef!.focus()"
          />
          <TaskTagDropdown></TaskTagDropdown>
        </div>
      </template>
      <template #suffix>
        <div
          v-if="!disableModule.includes('calendar')"
          class="u-font-size-smail u-fx u-gap5 u-fac"
        >
          <div v-if="createTaskInfo.taskStartDate">
            {{ jsUtil.dateFormatLabel(createTaskInfo.taskStartDate as string) }}
          </div>
          <div v-if="createTaskInfo.taskStartTime">
            {{ createTaskInfo.taskStartTime }}
          </div>
          <div v-if="createTaskInfo.taskEndDate">
            - {{ jsUtil.dateFormatLabel(createTaskInfo.taskEndDate as string) }}
          </div>
          <div v-if="createTaskInfo.taskEndTime">
            {{ createTaskInfo.taskEndTime }}
          </div>
          <TaskDateTimeTrigger :task-date-info="createTaskInfo"
                               @save="handleTaskDateChance">
            <a-link class="calendar" size="mini" style="padding: 4px">
              <iconpark-icon
                name="calendar"
                :style="{ color: createTaskInfo.taskStartDate ? 'rgb(var(--arcoblue-6))' : 'var(--color-neutral-4)' }"
              ></iconpark-icon>
            </a-link>
          </TaskDateTimeTrigger>
        </div>
      </template>
    </a-input>
  </div>
</template>
<style scoped lang="less">
.add-task {
  position: relative;
}
:deep(.arco-input-wrapper .arco-input-prefix) {
  padding-right: 4px;
}
</style>
