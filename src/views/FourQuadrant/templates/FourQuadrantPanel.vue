<script setup lang="ts">
import type { IPriorityOption } from "@/constants/tasks/TaskConstants.ts";
import type { TaskListItem } from "@xiaou66/todo-plugin";
import { TaskItem, useTaskDetailDrawer, useTaskViewProvide } from '@/components/Tasks';
import { useTemplateRef, onMounted, onBeforeMount } from 'vue';
import {combine} from "@atlaskit/pragmatic-drag-and-drop/combine";
import {
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { getGroupItemData } from '@/components/Tasks/TaskItem/TaskGroup.ts';
import { useTaskDragContext } from '@/components/Tasks/TaskItem/TaskDragWrapper.ts';

const props = defineProps<{
  taskLevelOption: IPriorityOption
  taskList: TaskListItem[]
}>();

const taskDetailDrawer = useTaskDetailDrawer();
function handleOpenTask(taskItem: TaskListItem) {
  taskDetailDrawer.show(taskItem);
}

const [taskDragContext, setTaskDragContext] = useTaskDragContext();
const taskListRef = useTemplateRef<HTMLDivElement>('taskListRef');

const taskViewProvide = useTaskViewProvide();
let cleanup = () => { };
onMounted(() => {
  cleanup = combine(
    dropTargetForElements({
      element: taskListRef.value!,
      getData(args) {
        return getGroupItemData({});
      },
      onDragEnter: () => {
        setTaskDragContext({
          newTask: {
            taskLevel: props.taskLevelOption.value
          }
        });
      },
    }),
    dropTargetForElements({
      element: document.documentElement,
      canDrop(args) {
        return true;
        // return false;
      },
      onDrop(args) {
        setTaskDragContext(undefined);
      },
    }),
  );
});

onBeforeMount(() => {
  cleanup();
})
</script>

<template>
  <div class="four-quadrant-item">
    <div class="header">
      <div class="u-fx u-gap5">
        <div>
          <iconpark-icon name="mark"
                         :color="taskLevelOption.color"
                         style="font-size: 18px; font-weight: 600;" />
        </div>
        <div :style="{color: taskLevelOption.color}" style="font-size: 15px">
          {{ taskLevelOption.title }}
        </div>
      </div>
    </div>
    <a-scrollbar class="u-h-full"
                 style="max-height: calc(100vh / 2 - 90px); overflow-y: auto;">
          <div class="u-h-full"
               ref="taskListRef">
              <div v-if="taskList && taskList.length">
                  <TaskItem v-for="taskItem in taskList"
                            :key="taskItem.taskId"
                            :task-item="taskItem"
                            show-type="default"
                            @click="handleOpenTask"
                            @dragStart="taskViewProvide.dragStart"
                            @dragEnd="taskViewProvide.dragEnd"
                            @save="taskViewProvide.saveTask" />
              </div>
              <div class="u-h-full u-fx u-fac" v-else>
                  <a-empty>无任务</a-empty>
              </div>
          </div>
      </a-scrollbar>
  </div>
</template>

<style scoped lang="less">
:deep(.task-item) {
  background: var(--color-bg-4);
}
.four-quadrant-item {
  display: grid;
  grid-template-rows: 24px 1fr;
  padding: 5px;
  border-radius: 10px;
  background: var(--color-bg-4);
  height: 100%;
}
</style>
