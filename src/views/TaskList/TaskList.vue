<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useTaskListRuntimeStore } from '@/views/TaskList/TaskList.ts';
import { TaskView } from '@/components/Tasks';
import type { IMenuItem } from './templates/Menu.ts';
import { TaskTopMenu, TaskGroupMenu, TaskBottomMenu, TaskTagMenu } from './templates';
import TaskFilterMenu from '@/views/TaskList/templates/TaskFilterMenu.vue';
import { Scrollbar } from '@xiaou66/u-web-ui';
// 当前选中的分类
const categoryMap = ref<Record<string, IMenuItem[]>>({
  top: [],
  group: [],
  taskTag: [],
  bottom: [],
  filter: [],
});
const categoryList = computed<IMenuItem[]>(() => {
  return (
    Object.keys(categoryMap.value)
      .map((key) => categoryMap.value[key])
      .flat() || []
  );
});
const currentCategoryId = ref<string>('toDay');
const currentCategory = computed(() => {
  return categoryList.value.find((item) => item.id === currentCategoryId.value);
});

function setCategoryList(type: string, menuList: IMenuItem[]) {
  console.log('menuList', menuList);
  if (menuList.length === (categoryMap.value[type] || []).length) {
    return;
  }
  categoryMap.value[type] = menuList;
}

const { collapsed } = useTaskListRuntimeStore();
</script>

<template>
  <div class="u-main-content">
    <div class="task-container">
      <!-- 左侧导航栏 -->
      <div class="sidebar" style="max-height: 100%" :class="{ collapsed }">
        <Scrollbar class="max-h-full">
          <div style="padding: 10px 6px 0;">
            <TaskTopMenu
              v-model:active="currentCategoryId"
              :setCategoryList="(list: IMenuItem[]) => setCategoryList('top', list)"
            />
            <TaskGroupMenu
              v-model:active="currentCategoryId"
              :setCategoryList="(list: IMenuItem[]) => setCategoryList('group', list)"
            />
            <TaskFilterMenu v-model:active="currentCategoryId"
                            :setCategoryList="(list: IMenuItem[]) => setCategoryList('filter', list)" />
            <TaskTagMenu
              v-model:active="currentCategoryId"
              :setCategoryList="(list: IMenuItem[]) => setCategoryList('taskTag', list)"
            />
            <TaskBottomMenu
              v-model:active="currentCategoryId"
              :setCategoryList="(list: IMenuItem[]) => setCategoryList('bottom', list)"
            >
            </TaskBottomMenu>
          </div>
        </Scrollbar>
      </div>

      <!-- 右侧任务列表 -->
      <div v-if="currentCategory" class="task-list">
        <TaskView
          :title="currentCategory.name"
          :subtitle="currentCategory.desc"
          :id="currentCategory.id"
          :type="currentCategory.menuType"
          :searchParams="currentCategory.searchParams"
          :defaultViewConfig="currentCategory.viewConfig"
          :scene="
            currentCategory.id !== 'finish' && currentCategory.id !== 'delete'
              ? 'default'
              : currentCategory.id
          "
        >
          <template #header-title-prefix>
            <div class="collapsed cursor-pointer">
              <div v-if="collapsed" class="u-fx u-fac" @click="collapsed = false">
                <t-icon class="i-u-expand-left text-2xl color-neutral u-hover"></t-icon>
              </div>
              <div v-else class="u-fx u-fac" @click="collapsed = true">
                <t-icon class="i-u-expand-right text-2xl color-neutral u-hover"></t-icon>
              </div>
            </div>
            <slot></slot>
          </template>
        </TaskView>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.u-web-page-header-mini) {
  padding: 8px 0;
}

.u-main-content {
  padding: 0;
  width: 100%;
  height: 100%;
}
.task-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.task-list {
  flex-grow: 1;
  padding: 6px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  box-sizing: border-box;
}
</style>
