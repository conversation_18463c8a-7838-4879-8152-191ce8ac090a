<script setup lang="ts">
import { onMounted } from 'vue';
import { TaskContextMenu, TaskDetailDrawer, UtoolsSearchView } from '@/components/Tasks';
import { startDrivers } from '@/utils/drivers';
import { AiTaskDrawer } from '@/components/ai';
import {useTaskSetting} from "@/views/Setting/TaskSetting.ts";
import { UtoolsLayout } from '@xiaou66/u-web-ui'
import { routes } from '@/router'


function refreshData() {
  window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
}


onMounted(() => {
  setTimeout(() => {
    const oldUser = utools.dbStorage.getItem('oldUser');
    if (!oldUser) {
      startDrivers();
      utools.dbStorage.setItem('oldUser', true);
    }
  }, 500)
})
const { guideDisable } = useTaskSetting();

const appName: string = import.meta.env.VITE_NAME;
const avatar = utools.getUser()?.avatar || 'logo.png';
function loadRouter() {
  return routes.filter(item => item.meta.menu);
}
</script>
<template>
    <TaskContextMenu />
    <AiTaskDrawer />
    <TaskDetailDrawer @close="refreshData" />
    <UtoolsSearchView />
    <UtoolsLayout :load-router="loadRouter"
                  :avatar="avatar"
                  :title="appName"
                  size="small" />
<!--    <div class="u-fx">-->
<!--      <LeftMenu />-->
<!--      <div id="content-wrapper">-->
<!--        &lt;!&ndash; 全局 header &ndash;&gt;-->
<!--        <div id="global-header">-->
<!--          <div></div>-->
<!--          <div v-if="!guideDisable"-->
<!--               id="use-user-count"-->
<!--               class="u-fx u-fac u-pointer u-gap5"-->
<!--               @click="startDrivers">-->
<!--            <iconpark-icon name="fireworks"></iconpark-icon>-->
<!--            进入操作引导-->
<!--          </div>-->
<!--  &lt;!&ndash;        <div  v-if="useUserCount"-->
<!--                id="use-user-count"-->
<!--                class="u-fx u-fac u-pointer"-->
<!--                @click="handlePlugInHome">-->
<!--            <icon-fire size="14" style="color: #c12c1f" />-->
<!--            你正在与-->
<!--            <a-tooltip>-->
<!--              <template #content>-->
<!--                <span style="font-size: 12px">数据来自 uTools 插件统计提供</span>-->
<!--              </template>-->
<!--              <span>{{useUserCount}}</span>-->
<!--            </a-tooltip>-->
<!--            位小伙伴一起使用</div>&ndash;&gt;-->
<!--        </div>-->
<!--        <div id="content">-->
<!--          <router-view />-->
<!--        </div>-->
<!--        <div id="global-footer">-->
<!--          <div class="u-fx">-->
<!--            <div> © {{new Date().getFullYear()}} [xiaou]。保留所有权利</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
</template>

<style lang="less" scoped>
body[arco-theme="dark"] {
  #content-wrapper {
    #content {
      background: var(--color-neutral-2);
    }
  }
}
#content-wrapper {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  padding: 10px 4px;
  display: grid;
  grid-template-rows: 15px 1fr 8px;
  #global-header {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px 0 20px;
    position: relative;
    top: -6px;
    #use-user-count {
      font-size: 12px;
      span {
        padding: 0 2px;
        font-weight: 700;
      }
    }
  }
  #content {
    box-sizing: border-box;
    background: var(--main-background-transparent);
    border-radius: 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
    max-height: 100%;
    overflow-y: auto;
    height: 100%;
  }
  #global-footer {
    position: relative;
    bottom: -5px;
    >div {
      justify-content: center;
      font-size: 8px;
    }
  }
}
</style>
